.filter-panel {
  background: linear-gradient(135deg, rgba(30, 41, 59, 0.95) 0%, rgba(15, 23, 42, 0.95) 100%);
  border-right: 1px solid rgba(255, 255, 255, 0.08);
  height: 100vh;
  position: fixed;
  left: 0;
  top: 0;
  z-index: 1000;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  overflow-y: auto;
  color: #ffffff;
  backdrop-filter: blur(12px);
  box-shadow: 0 0 30px rgba(0, 0, 0, 0.2);
}

.filter-panel.open {
  width: 320px;
}

.filter-panel.closed {
  width: 50px;
}

.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.08);
  background: rgba(255, 255, 255, 0.02);
  backdrop-filter: blur(12px);
  position: sticky;
  top: 0;
  z-index: 1001;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.filter-header h2 {
  margin: 0;
  font-size: 1.4em;
  background: linear-gradient(45deg, #8b5cf6, #c084fc);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 700;
  letter-spacing: -0.01em;
  transition: opacity 0.3s ease, transform 0.3s ease;
}

.filter-panel.closed .filter-header h2 {
  display: none;
  opacity: 0;
  transform: translateX(-20px);
}

.toggle-button {
  background: rgba(139, 92, 246, 0.1);
  border: none;
  color: #8b5cf6;
  padding: 8px;
  cursor: pointer;
  font-size: 1.2em;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 8px;
  outline: none;
}

.toggle-button:hover {
  transform: translateY(-2px) scale(1.05);
  background: rgba(139, 92, 246, 0.25);
}

.toggle-button:focus {
  background: rgba(139, 92, 246, 0.2);
  box-shadow: none;
}

.filter-content {
  padding: 16px;
  animation: fadeIn 0.4s ease-out;
}

.reset-button {
  width: 100%;
  background: linear-gradient(45deg, #ef4444, #dc2626);
  border: none;
  color: #ffffff;
  padding: 12px;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 600;
  margin-bottom: 24px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 6px -1px rgba(239, 68, 68, 0.2);
  outline: none;
}

.reset-button:hover {
  transform: translateY(-2px) scale(1.02);
  box-shadow: 0 8px 12px -1px rgba(239, 68, 68, 0.4);
}

.reset-button:focus {
  box-shadow: none;
}

.filter-section {
  margin-bottom: 16px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.08);
  padding-bottom: 16px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.filter-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

.filter-section h3 {
  margin: 0 0 12px 0;
  font-size: 1.1em;
  color: #8b5cf6;
  font-weight: 600;
  letter-spacing: -0.01em;
  display: flex;
  align-items: center;
  gap: 8px;
  text-transform: capitalize;
  transition: color 0.3s ease;
}

.filter-section h4 {
  margin: 12px 0 8px 0;
  font-size: 0.95em;
  color: #94a3b8;
  font-weight: 500;
  text-transform: capitalize;
}

.filter-description {
  font-size: 0.85em;
  color: #64748b;
  margin: 0 0 12px 0;
  font-style: italic;
  line-height: 1.5;
}

.checkbox-group {
  display: flex;
  flex-direction: column;
  margin-top: 8px;
  gap: 8px;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 0.95em;
  cursor: pointer;
  padding: 8px 12px;
  margin-bottom: 2px;
  border-radius: 8px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  color: #e2e8f0;
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.05);
  text-transform: capitalize;
}

.checkbox-label:hover {
  transform: translateX(4px) scale(1.02);
  background: rgba(139, 92, 246, 0.1);
  border-color: rgba(139, 92, 246, 0.3);
}

.checkbox-label input[type="checkbox"] {
  appearance: none;
  -webkit-appearance: none;
  width: 18px;
  height: 18px;
  border: 2px solid rgba(139, 92, 246, 0.3);
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.05);
  cursor: pointer;
  position: relative;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.checkbox-label input[type="checkbox"]:checked {
  background: #8b5cf6;
  border-color: #8b5cf6;
  transform: scale(1.1);
}

.checkbox-label input[type="checkbox"]:checked::after {
  content: '';
  position: absolute;
  left: 5px;
  top: 2px;
  width: 4px;
  height: 8px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

.checkbox-label input[type="checkbox"]:hover {
  border-color: #8b5cf6;
  background: rgba(139, 92, 246, 0.1);
}

.checkbox-label input[type="checkbox"]:focus {
  outline: none;
  box-shadow: none;
}

.range-group {
  display: flex;
  flex-direction: column;
  margin-top: 8px;
  gap: 12px;
  padding: 16px;
  background: rgba(255, 255, 255, 0.02);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.05);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.range-group:hover {
  background: rgba(255, 255, 255, 0.04);
  border-color: rgba(139, 92, 246, 0.2);
}

.range-group label {
  display: flex;
  flex-direction: column;
  gap: 8px;
  font-size: 0.95em;
  color: #e2e8f0;
  text-transform: capitalize;
}

.range-group input[type="range"] {
  -webkit-appearance: none;
  width: 100%;
  height: 6px;
  border-radius: 3px;
  background: rgba(255, 255, 255, 0.1);
  outline: none;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.range-group input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: #8b5cf6;
  cursor: pointer;
  border: 2px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.range-group input[type="range"]::-moz-range-thumb {
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: #8b5cf6;
  cursor: pointer;
  border: 2px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.range-group input[type="range"]:hover::-webkit-slider-thumb {
  transform: scale(1.2);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.4);
}

.range-group input[type="range"]:hover::-moz-range-thumb {
  transform: scale(1.2);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.4);
}

.range-group input[type="range"]:focus {
  outline: none;
}

.range-group input[type="range"]:focus::-webkit-slider-thumb {
  box-shadow: none;
}

.range-group input[type="range"]:focus::-moz-range-thumb {
  box-shadow: none;
}

.skill-range {
  margin-bottom: 20px;
  padding: 16px;
  background: rgba(255, 255, 255, 0.02);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.05);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.skill-range:hover {
  background: rgba(255, 255, 255, 0.04);
  border-color: rgba(139, 92, 246, 0.2);
}

/* Custom scrollbar */
.filter-panel::-webkit-scrollbar {
  width: 6px;
}

.filter-panel::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.02);
}

.filter-panel::-webkit-scrollbar-thumb {
  background: rgba(139, 92, 246, 0.3);
  border-radius: 3px;
  transition: background 0.3s ease;
}

.filter-panel::-webkit-scrollbar-thumb:hover {
  background: rgba(139, 92, 246, 0.5);
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .filter-panel.open {
    width: 100%;
    height: 100vh;
  }
  
  .filter-panel.closed {
    width: 40px;
  }
  
  .filter-header {
    padding: 20px;
  }
  
  .filter-content {
    padding: 20px;
  }
  
  .checkbox-label {
    padding: 6px 10px;
    font-size: 0.9em;
  }
  
  .range-group {
    padding: 10px;
  }
}

.filter-section-header {
  display: flex;
  justify-content: space-between;
  border-radius: 8px;
  align-items: center;
  padding: 8px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.filter-section-header h3 {
  margin: 0;
}

.filter-section-header:hover {
  background-color: rgba(255, 255, 255, 0.05);
  transform: scale(1.05);
}

.toggle-icon {
  font-size: 12px;
  color: #666;
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.checkbox-group
.range-group {
  overflow: hidden;
  margin-top: 8px;
  transition: max-height 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.checkbox-group > *, .range-group > * {
  opacity: 0;
  transform: translateY(10px);
  animation: staggerFadeIn 0.2s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.checkbox-group > *:nth-child(1), .range-group > *:nth-child(1) { animation-duration: 0.0s; }
.checkbox-group > *:nth-child(2), .range-group > *:nth-child(2) { animation-duration: 0.05s; }
.checkbox-group > *:nth-child(3), .range-group > *:nth-child(3) { animation-duration: 0.1s; }
.checkbox-group > *:nth-child(4), .range-group > *:nth-child(4) { animation-duration: 0.15s; }
.checkbox-group > *:nth-child(5), .range-group > *:nth-child(5) { animation-duration: 0.2s; }
.checkbox-group > *:nth-child(6), .range-group > *:nth-child(6) { animation-duration: 0.25s; }
.checkbox-group > *:nth-child(7), .range-group > *:nth-child(7) { animation-duration: 0.3s; }
.checkbox-group > *:nth-child(8), .range-group > *:nth-child(8) { animation-duration: 0.35s; }
.checkbox-group > *:nth-child(9), .range-group > *:nth-child(9) { animation-duration: 0.4s; }
.checkbox-group > *:nth-child(10), .range-group > *:nth-child(10) { animation-duration: 0.45s; }

@keyframes staggerFadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.select-major-ids-button {
  margin-top: 8px;
  padding: 6px 12px;
  background-color: #ffffff05;
  color: #e2e8f0;
  border: 1px solid rgba(255,255,255,.05);
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  width: 100%;
  text-align: left;
}

.select-major-ids-button:hover {
  transform: translateX(4px) scale(1.02);
  background: rgba(139, 92, 246, 0.1);
  border-color: rgba(139, 92, 246, 0.3);
}

/* Item Type Filter Styles */
.item-type-filters {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.item-type-category {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.item-type-category h4 {
  margin: 0;
  font-size: 0.9em;
  color: #94a3b8;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.item-type-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.item-type-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  padding: 8px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 60px;
  color: #94a3b8;
  font-size: 0.75em;
  font-weight: 500;
}

.item-type-button:hover {
  background: rgba(139, 92, 246, 0.1);
  border-color: rgba(139, 92, 246, 0.3);
  transform: translateY(-1px);
}

.item-type-button.active {
  background: rgba(139, 92, 246, 0.2);
  border-color: #8b5cf6;
  color: #c084fc;
  box-shadow: 0 0 12px rgba(139, 92, 246, 0.3);
}

.item-type-button span {
  text-align: center;
  line-height: 1.2;
}
