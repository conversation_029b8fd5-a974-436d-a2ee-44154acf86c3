:root {
  font-family: '<PERSON><PERSON><PERSON> UI', Tahoma, Geneva, Verdana, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  color-scheme: dark;
  color: rgba(255, 255, 255, 0.87);
  background-color: #0a0a0a;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  min-width: 320px;
  min-height: 100vh;
  background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%);
  color: #ffffff;
  overflow-x: hidden;
}

#root {
  width: 100%;
  min-height: 100vh;
}

h1, h2, h3, h4, h5, h6 {
  margin: 0;
  font-weight: 600;
}

button {
  border-radius: 6px;
  border: 1px solid transparent;
  padding: 0.6em 1.2em;
  font-size: 1em;
  font-weight: 500;
  font-family: inherit;
  background-color: #333;
  color: #ffffff;
  cursor: pointer;
  transition: all 0.25s ease;
}

button:hover {
  background-color: #444;
}

button:focus,
button:focus-visible {
  outline: 2px solid #a855f7;
  outline-offset: 2px;
}

button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

input[type="text"],
input[type="search"] {
  font-family: inherit;
  font-size: 1em;
  border-radius: 6px;
  border: 1px solid #444;
  background-color: #1a1a1a;
  color: #ffffff;
  padding: 0.5em;
}

input[type="text"]:focus,
input[type="search"]:focus {
  outline: 2px solid #a855f7;
  outline-offset: 2px;
  border-color: #a855f7;
}

/* Scrollbar styling */
::-webkit-scrollbar {
  width: 12px;
}

::-webkit-scrollbar-track {
  background: #1a1a1a;
}

::-webkit-scrollbar-thumb {
  background: #444;
  border-radius: 6px;
}

::-webkit-scrollbar-thumb:hover {
  background: #555;
}

/* Selection styling */
::selection {
  background: rgba(168, 85, 247, 0.3);
  color: #ffffff;
}

/* Minecraft font import */
@import url('https://fonts.googleapis.com/css2?family=Press+Start+2P&display=swap');

@font-face {
  font-family: 'Minecraftia';
  src: url('/fonts/Minecraftia.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
}
