.app {
  display: flex;
  min-height: 100vh;
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
  color: #ffffff;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.main-content {
  flex: 1;
  padding: 0px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  min-height: 100vh;
  overflow-x: hidden;
  background: rgba(255, 255, 255, 0.02);
  backdrop-filter: blur(10px);
}

.main-content.with-sidebar {
  margin-left: 320px;
}

.main-content.full-width {
  margin-left: 50px;
}

.app-header {
  text-align: center;
  margin-bottom: 40px;
  padding: 40px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  background: linear-gradient(180deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0) 100%);
}

.app-header h1 {
  font-size: 3em;
  margin: 0 0 12px 0;
  background: linear-gradient(45deg, #8b5cf6, #c084fc);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 0 30px rgba(139, 92, 246, 0.3);
  letter-spacing: -0.02em;
  font-weight: 800;
}

.app-header p {
  font-size: 1.2em;
  color: #94a3b8;
  margin: 0;
  font-weight: 400;
  line-height: 1.6;
}

.loading-container,
.error-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
  color: #ffffff;
  text-align: center;
  padding: 40px;
}

.loading-spinner {
  width: 60px;
  height: 60px;
  border: 3px solid rgba(139, 92, 246, 0.2);
  border-top: 3px solid #8b5cf6;
  border-radius: 50%;
  animation: spin 1s cubic-bezier(0.4, 0, 0.2, 1) infinite;
  margin-bottom: 32px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-container h2,
.error-container h2 {
  color: #8b5cf6;
  margin-bottom: 20px;
  font-size: 1.8em;
  font-weight: 700;
}

.loading-container p,
.error-container p {
  color: #94a3b8;
  font-size: 1.1em;
  margin-bottom: 32px;
  max-width: 500px;
  line-height: 1.6;
}

.error-container button {
  background: #ef4444;
  border: none;
  color: #ffffff;
  padding: 14px 28px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 1em;
  font-weight: 600;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 6px -1px rgba(239, 68, 68, 0.2);
}

.error-container button:hover {
  background: #dc2626;
  transform: translateY(-1px);
  box-shadow: 0 6px 8px -1px rgba(239, 68, 68, 0.3);
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .main-content.with-sidebar {
    margin-left: 0;
  }

  .main-content.full-width {
    margin-left: 40px;
  }

  .main-content {
    padding: 20px;
  }

  .app-header {
    padding: 32px 0;
    margin-bottom: 32px;
  }

  .app-header h1 {
    font-size: 2.5em;
  }

  .app-header p {
    font-size: 1.1em;
    padding: 0 20px;
  }
}
