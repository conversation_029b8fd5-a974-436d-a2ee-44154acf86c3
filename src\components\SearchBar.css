.search-bar-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 24px;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

.search-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.search-input {
  width: 100%;
  padding: 14px 20px;
  font-size: 1rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.03);
  color: #ffffff;
  outline: none;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10px);
}

.search-input:focus {
  border-color: rgba(255, 255, 255, 0.2);
  background: rgba(255, 255, 255, 0.05);
  box-shadow: none;
}

.search-input::placeholder {
  color: #64748b;
}

.search-icon {
  position: absolute;
  right: 16px;
  width: 20px;
  height: 20px;
  color: #64748b;
  pointer-events: none;
  transition: color 0.2s ease;
}

.search-icon svg {
  width: 100%;
  height: 100%;
}

.search-input:focus + .search-icon {
  color: #8b5cf6;
}

.search-results-info {
  font-size: 0.95rem;
  color: #94a3b8;
  text-align: center;
  padding: 8px;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .search-bar-container {
    gap: 8px;
    margin-bottom: 20px;
  }

  .search-input {
    padding: 12px 16px;
    font-size: 0.95rem;
  }
  
  .search-icon {
    right: 14px;
    width: 18px;
    height: 18px;
  }
  
  .search-results-info {
    font-size: 0.9rem;
    padding: 6px;
  }
}
