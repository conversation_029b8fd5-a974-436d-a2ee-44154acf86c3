.item-grid-container {
  width: 100%;
  max-width: 1600px;
  margin: 0 auto;
  padding: 0 20px;
}

.grid-controls {
  background: rgba(255, 255, 255, 0.03);
  padding: 20px;
  border-radius: 12px;
  margin-bottom: 24px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.sort-controls {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.sort-controls span {
  color: #94a3b8;
  font-weight: 500;
  font-size: 0.95em;
}

.sort-button {
  background: rgba(139, 92, 246, 0.1);
  border: none;
  color: #8b5cf6;
  padding: 8px 16px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 0.9em;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  gap: 6px;
  outline: none;
}

.sort-button:hover {
  background: rgba(139, 92, 246, 0.2);
  transform: translateY(-1px);
}

.sort-button:focus {
  background: rgba(139, 92, 246, 0.2);
  box-shadow: 0 0 0 2px rgba(139, 92, 246, 0.2);
}

.sort-button.active {
  background: #8b5cf6;
  color: #ffffff;
  box-shadow: 0 4px 6px -1px rgba(139, 92, 246, 0.2);
}

.sort-direction {
  font-weight: 600;
}

.item-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 24px;
  margin-bottom: 40px;
  padding: 4px;
}

.no-items {
  text-align: center;
  padding: 80px 32px;
  color: #ffffff;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.no-items h2 {
  color: #ef4444;
  margin-bottom: 16px;
  font-size: 1.8em;
  font-weight: 700;
}

.no-items p {
  color: #94a3b8;
  font-size: 1.1em;
  line-height: 1.6;
}

.load-more-trigger {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
}

.loading-more {
  color: #94a3b8;
  font-size: 0.95em;
  padding: 12px;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.page-info {
  text-align: center;
  color: #94a3b8;
  font-size: 0.95em;
  margin-bottom: 40px;
  padding: 12px;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .item-grid {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 16px;
  }
  
  .grid-controls {
    padding: 16px;
  }
  
  .sort-controls {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .sort-controls span {
    margin-bottom: 4px;
  }
  
  .sort-button {
    padding: 6px 12px;
    font-size: 0.85em;
  }
}

@media (max-width: 480px) {
  .item-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
}
